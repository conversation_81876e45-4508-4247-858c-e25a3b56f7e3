// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "server/api/web/v1"
)

type (
	IRand interface {
		// Tgrj 随机输出一条舔狗日记语录
		Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error)
		WangyiHot(ctx context.Context, req *v1.WangyiHotReq) (res *v1.WangyiHotRes, err error)
		AnimePic(ctx context.Context, req *v1.AnimePicReq) (res *v1.AnimePicRes, err error)
	}
	IVideo interface {
		DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error)
		VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error)
	}
)

var (
	localRand  IRand
	localVideo IVideo
)

func Rand() IRand {
	if localRand == nil {
		panic("implement not found for interface IRand, forgot register?")
	}
	return localRand
}

func RegisterRand(i IRand) {
	localRand = i
}

func Video() IVideo {
	if localVideo == nil {
		panic("implement not found for interface IVideo, forgot register?")
	}
	return localVideo
}

func RegisterVideo(i IVideo) {
	localVideo = i
}
