package web

import (
	"context"
	v1 "server/api/web/v1"
	"server/internal/dao"
	"server/internal/model/entity"
	"server/internal/service"
	"server/utility"
	"strings"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gctx"
)

// sRand 随机语录服务实现
// 结构体名必须以 s 开头，后跟大写字母，这样 gf gen service 才能识别
type sRand struct{}

func New() *sRand {
	return &sRand{}
}

func init() {
	service.RegisterRand(New())
}

// Tgrj 随机输出一条舔狗日记语录
func (s *sRand) Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error) {

	// 查询随机舔狗日记
	var dogWord *entity.DogWords
	err = dao.DogWords.Ctx(ctx).OrderRandom().Scan(&dogWord)
	if err != nil {
		return nil, gerror.Wrap(err, "不舔了，不舔了")
	}
	return &v1.TgrjRes{Content: dogWord.Content}, nil
}

func (s *sRand) WangyiHot(ctx context.Context, req *v1.WangyiHotReq) (res *v1.WangyiHotRes, err error) {
	apiUrl := "https://api.dragonlongzhu.cn/api/wangyi_hot_review.php"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()
	client := utility.GetOptimizedClient()

	// 发起HTTP请求
	response, err := client.
		Timeout(8*time.Second).
		Header(utility.GetStandardHeaders()).
		Get(independentCtx, apiUrl)
	if err != nil {
		return nil, gerror.Wrap(err, "网易热评API请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		msg := json.Get("msg").String()
		return nil, gerror.Newf("返回错误: %s", msg)
	}

	// 直接映射到结构体
	result := &v1.WangyiHotRes{}
	err = gjson.DecodeTo(responseBody, result)
	if err != nil {
		return nil, gerror.Wrap(err, "解析数据失败")
	}

	// 将HTTP URL转换为HTTPS，解决前端安全问题
	if result.Url != "" {
		result.Url = strings.Replace(result.Url, "http://", "https://", 1)
	}
	if result.Link != "" {
		result.Link = strings.Replace(result.Link, "http://", "https://", 1)
	}

	return result, nil
}

func (s *sRand) AnimePic(ctx context.Context, req *v1.AnimePicReq) (res *v1.AnimePicRes, err error) {
	apiUrl := "https://v2.api-m.com/api/randomAcgPic?type=pc"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()
	client := utility.GetOptimizedClient()

	// 发起HTTP请求
	response, err := client.
		Timeout(8*time.Second).
		Header(utility.GetStandardHeaders()).
		Get(independentCtx, apiUrl)
	if err != nil {
		return nil, gerror.Wrap(err, "动漫图片API请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		msg := json.Get("msg").String()
		return nil, gerror.Newf("返回错误: %s", msg)
	}

	// 获取图片URL
	imgUrl := json.Get("data").String()
	if imgUrl == "" {
		return nil, gerror.New("动漫图片API返回空URL")
	}

	return &v1.AnimePicRes{Imgurl: imgUrl}, nil
}
