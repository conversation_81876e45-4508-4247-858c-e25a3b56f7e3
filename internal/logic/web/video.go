package web

import (
	"context"
	v1 "server/api/web/v1"
	"server/internal/service"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

type sVideo struct{}

func NewVideo() *sVideo {
	return &sVideo{}
}

func init() {
	service.RegisterVideo(NewVideo())
}

func (s *sVideo) DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error) {
	apiUrl := "https://api.makuo.cc/api/get.video.douyin"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()

	// 发起HTTP请求
	response, err := g.Client().
		Timeout(8*time.Second).
		Header(map[string]string{
			"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			"Accept":     "*/*",
		}).
		Get(independentCtx, apiUrl, g.Map{
			"token": "qPL7zM0Osfq1-W6gyQ6VSQ",
			"url":   req.Url,
		})
	if err != nil {
		return nil, gerror.Wrap(err, "抖音视频解析API请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		msg := json.Get("msg").String()
		return nil, gerror.Newf("请输入正确的链接: %s", msg)
	}

	// 提取数据并映射到结构体
	dataJson := json.Get("data")
	result := &v1.DouyinVideoRes{}
	err = dataJson.Struct(result)
	if err != nil {
		return nil, gerror.Wrap(err, "解析响应数据失败")
	}

	return result, nil
}

func (s *sVideo) VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error) {
	apiUrl := "https://api.dragonlongzhu.cn/api/sp_jx/sp.php"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()

	// 发起HTTP请求
	response, err := g.Client().
		Timeout(8*time.Second).
		Header(map[string]string{
			"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			"Accept":     "*/*",
		}).
		Get(independentCtx, apiUrl, g.Map{
			"url": req.Url,
		})
	if err != nil {
		return nil, gerror.Wrap(err, "请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		// 尝试备用API
		backupApiUrl := "https://api.dragonlongzhu.cn/api/sp_jx/tuji.php"

		backupResponse, backupErr := g.Client().
			Timeout(8*time.Second).
			Header(map[string]string{
				"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				"Accept":     "*/*",
			}).
			Get(independentCtx, backupApiUrl, g.Map{
				"url": req.Url,
			})
		if backupErr != nil {
			msg := json.Get("msg").String()
			return nil, gerror.Newf("请输入正确的链接: %s", msg)
		}
		defer backupResponse.Close()

		// 解析备用API响应
		backupResponseBody := backupResponse.ReadAllString()
		backupJson := gjson.New(backupResponseBody)

		// 检查备用API状态
		if backupJson.Get("code").Int() != 200 {
			msg := backupJson.Get("msg").String()
			return nil, gerror.Newf("请输入正确的链接: %s", msg)
		}

		// 使用备用API的数据
		json = backupJson
	}

	// 提取数据并映射到结构体
	dataJson := json.Get("data")
	result := make(v1.VipVideoRes)
	err = dataJson.Struct(&result)
	if err != nil {
		return nil, gerror.Wrap(err, "解析响应数据失败")
	}

	return &result, nil
}

func (s *sVideo) DownLoad(ctx context.Context, req *v1.DownLoadReq) (res *v1.DownLoadRes, err error) {
	// 记录开始日志
	g.Log().Info(ctx, "=== Starting download proxy service ===")
	g.Log().Info(ctx, "Request URL:", req.Url)
	g.Log().Info(ctx, "User Agent:", g.RequestFromCtx(ctx).Header.Get("User-Agent"))

	// 使用独立上下文避免取消传播
	independentCtx := gctx.New()
	client := g.Client()

	// 第一步：获取头信息
	g.Log().Info(ctx, "Fetching headers...")
	headResponse, err := client.
		Timeout(20*time.Second).
		Header(map[string]string{
			"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Accept":          "application/json, text/plain, */*",
			"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
			"Accept-Encoding": "gzip, deflate, br",
			"Connection":      "keep-alive",
			"Cache-Control":   "no-cache",
			"Pragma":          "no-cache",
		}).
		Head(independentCtx, req.Url)

	if err != nil {
		g.Log().Error(ctx, "CURL Error:", err)
		return nil, gerror.Wrap(err, "获取资源头信息失败")
	}
	defer headResponse.Close()

	// 检查HTTP状态码
	httpCode := headResponse.StatusCode
	g.Log().Info(ctx, "Response HTTP Code:", httpCode)

	if httpCode != 200 {
		g.Log().Error(ctx, "Error: HTTP Code", httpCode)
		return nil, gerror.Newf("HTTP请求失败，状态码: %d", httpCode)
	}

	// 获取内容类型
	contentType := headResponse.Header.Get("Content-Type")
	g.Log().Info(ctx, "Content Type:", contentType)

	// 第二步：获取完整内容
	g.Log().Info(ctx, "Downloading content...")
	response, err := client.
		Timeout(30*time.Second).
		Header(map[string]string{
			"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Accept":          "application/json, text/plain, */*",
			"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
			"Accept-Encoding": "gzip, deflate, br",
			"Connection":      "keep-alive",
			"Cache-Control":   "no-cache",
			"Pragma":          "no-cache",
		}).
		Get(independentCtx, req.Url)

	if err != nil {
		g.Log().Error(ctx, "CURL Error:", err)
		return nil, gerror.Wrap(err, "下载资源失败")
	}
	defer response.Close()

	// 读取响应内容
	content := response.ReadAll()
	if len(content) == 0 {
		g.Log().Error(ctx, "Empty response content")
		return nil, gerror.New("响应内容为空")
	}

	g.Log().Info(ctx, "Download completed successfully, content length:", len(content))

	// 返回结果
	return &v1.DownLoadRes{
		ContentType: contentType,
		Content:     content,
	}, nil
}
