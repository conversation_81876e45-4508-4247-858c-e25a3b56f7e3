package web

import (
	"context"
	v1 "server/api/web/v1"
	"server/internal/service"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

type sVideo struct{}

func NewVideo() *sVideo {
	return &sVideo{}
}

func init() {
	service.RegisterVideo(NewVideo())
}

func (s *sVideo) DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error) {
	apiUrl := "https://api.makuo.cc/api/get.video.douyin"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()

	// 发起HTTP请求
	response, err := g.Client().
		Timeout(8*time.Second).
		Header(map[string]string{
			"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			"Accept":     "*/*",
		}).
		Get(independentCtx, apiUrl, g.Map{
			"token": "qPL7zM0Osfq1-W6gyQ6VSQ",
			"url":   req.Url,
		})
	if err != nil {
		return nil, gerror.Wrap(err, "抖音视频解析API请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		msg := json.Get("msg").String()
		return nil, gerror.Newf("请输入正确的链接: %s", msg)
	}

	// 提取数据并映射到结构体
	dataJson := json.Get("data")
	result := &v1.DouyinVideoRes{}
	err = dataJson.Struct(result)
	if err != nil {
		return nil, gerror.Wrap(err, "解析响应数据失败")
	}

	return result, nil
}

func (s *sVideo) VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error) {
	apiUrl := "https://api.dragonlongzhu.cn/api/sp_jx/sp.php"

	// 使用独立上下文避免取消传播，解决连续请求问题
	independentCtx := gctx.New()

	// 发起HTTP请求
	response, err := g.Client().
		Timeout(8*time.Second).
		Header(map[string]string{
			"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			"Accept":     "*/*",
		}).
		Get(independentCtx, apiUrl, g.Map{
			"url": req.Url,
		})
	if err != nil {
		return nil, gerror.Wrap(err, "请求失败")
	}
	defer response.Close()

	// 解析响应
	responseBody := response.ReadAllString()
	json := gjson.New(responseBody)

	// 检查API状态
	if json.Get("code").Int() != 200 {
		// 尝试备用API
		backupApiUrl := "https://api.dragonlongzhu.cn/api/sp_jx/tuji.php"
		
		backupResponse, backupErr := g.Client().
			Timeout(8*time.Second).
			Header(map[string]string{
				"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				"Accept":     "*/*",
			}).
			Get(independentCtx, backupApiUrl, g.Map{
				"url": req.Url,
			})
		if backupErr != nil {
			msg := json.Get("msg").String()
			return nil, gerror.Newf("请输入正确的链接: %s", msg)
		}
		defer backupResponse.Close()

		// 解析备用API响应
		backupResponseBody := backupResponse.ReadAllString()
		backupJson := gjson.New(backupResponseBody)

		// 检查备用API状态
		if backupJson.Get("code").Int() != 200 {
			msg := backupJson.Get("msg").String()
			return nil, gerror.Newf("请输入正确的链接: %s", msg)
		}

		// 使用备用API的数据
		json = backupJson
	}

	// 提取数据并映射到结构体
	dataJson := json.Get("data")
	result := make(v1.VipVideoRes)
	err = dataJson.Struct(&result)
	if err != nil {
		return nil, gerror.Wrap(err, "解析响应数据失败")
	}

	return &result, nil
}

func (s *sVideo)DownLoad(ctx context.Context,req *v1.downLoadReq) (res *v1.downLoadRes, err error) {
	return nil, nil
}
