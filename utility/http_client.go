package utility

import (
	"net/http"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
)

var (
	optimizedClient *gclient.Client
	once            sync.Once
)

// GetOptimizedClient 获取优化配置的HTTP客户端
// 使用sync.Once确保Transport配置只执行一次，解决并发安全问题
func GetOptimizedClient() *gclient.Client {
	once.Do(func() {
		optimizedClient = g.Client()

		// 配置连接池参数，提高连接复用效率
		if transport, ok := optimizedClient.Transport.(*http.Transport); ok {
			transport.MaxIdleConnsPerHost = 10                 // 每个主机最大空闲连接数
			transport.MaxIdleConns = 100                       // 总的最大空闲连接数
			transport.IdleConnTimeout = 90 * time.Second       // 空闲连接超时时间
			transport.DisableKeepAlives = false                // 启用Keep-Alive
			transport.MaxConnsPerHost = 0                      // 每个主机最大连接数，0表示无限制
			transport.ResponseHeaderTimeout = 20 * time.Second // 响应头超时时间，调整为20秒避免与请求超时冲突
		}
	})

	return optimizedClient
}

// GetStandardHeaders 获取标准的HTTP请求头
func GetStandardHeaders() map[string]string {
	return map[string]string{
		"User-Agent":      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Accept":          "application/json, text/plain, */*",
		"Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
		"Accept-Encoding": "gzip, deflate, br",
		"Connection":      "keep-alive",
		"Cache-Control":   "no-cache",
		"Pragma":          "no-cache",
	}
}
