package utility

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

var (
	loggerMutex sync.Mutex
	logFile     *os.File
	logFilePath string
)

// InitDownloadLogger 初始化下载日志记录器
func InitDownloadLogger() error {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	// 创建logs目录
	logDir := "logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 设置日志文件路径
	logFilePath = filepath.Join(logDir, "download_log.txt")

	// 打开或创建日志文件
	var err error
	logFile, err = os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	return nil
}

// WriteDownloadLog 写入下载日志
func WriteDownloadLog(message string) {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logMessage := fmt.Sprintf("[%s] %s\n", timestamp, message)

	// 尝试写入日志文件
	if logFile != nil {
		if _, err := logFile.WriteString(logMessage); err != nil {
			// 如果写入失败，输出到控制台
			g.Log().Error(nil, "写入日志文件失败:", err)
		} else {
			// 立即刷新到磁盘
			logFile.Sync()
		}
	}

	// 同时输出到GoFrame日志系统
	g.Log().Info(nil, "DownloadProxy:", message)
}

// CloseDownloadLogger 关闭日志记录器
func CloseDownloadLogger() {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if logFile != nil {
		logFile.Close()
		logFile = nil
	}
}

// GetLogFilePath 获取日志文件路径
func GetLogFilePath() string {
	return logFilePath
}
