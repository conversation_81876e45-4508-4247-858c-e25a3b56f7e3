package v1

import "github.com/gogf/gf/v2/frame/g"

type TgrjReq struct {
	g.<PERSON>a `path:"/rand/dog" method:"get" summary:"舔狗日记" tags:"舔狗日记"`
}
type TgrjRes struct {
	Content string `json:"content"`
}

type WangyiHotReq struct {
	g.<PERSON>a `path:"/rand/wangyi/hot" method:"get" summary:"网易云音乐" tags:"随机网易云音乐"`
}
type WangyiHotRes struct {
	Img    string `json:"img"`
	Song   string `json:"song"`
	Singer string `json:"singer"`
	Url    string `json:"url"`
	Link   string `json:"link"`
}
type AnimePicReq struct {
	g.Meta `path:"/rand/anime/pic" method:"get" summary:"随机动漫图片" tags:"随机动漫图片"`
}
type AnimePicRes struct {
	Imgurl string `json:"imgurl" description:"图片地址"`
}
