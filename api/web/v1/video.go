package v1

import "github.com/gogf/gf/v2/frame/g"

type DouyinVideoReq struct {
	g.<PERSON>a `path:"/douyin/video" method:"get" summary:"抖音视频" tags:"抖音视频"`
	Url    string `json:"url" v:"required|url#请输入正确的链接|你要不要看看你输入的什么???" dc:"抖音视频链接"`
}
type DouyinVideoRes struct {
	Title    string   `json:"title"`
	Author   string   `json:"author"`
	Like     int      `json:"like"`
	Time     int      `json:"time"`
	VideoURL string   `json:"video_url,omitempty"`
	Count    int      `json:"count,omitempty"`
	Images   []string `json:"images,omitempty"`
}

type VipVideoReq struct {
	g.Meta `path:"/vip/video" method:"get" summary:"聚合解析" tags:"聚合解析"`
	Url    string `json:"url" v:"required|url#请输入正确的链接|你要不要看看你输入的什么???" dc:"聚合解析"`
}

type VipVideoRes map[string]interface{}

type DownLoadReq struct {
	g.<PERSON>a `path:"/download" method:"get" summary:"代理下载" tags:"代理下载"`
	Url    string `json:"url" v:"required|url#请输入正确的链接|你要不要看看你输入的什么???" dc:"下载链接"`
}

// DownLoadRes 代理下载响应 - 直接返回文件内容，不使用JSON格式
type DownLoadRes struct {
	ContentType string `json:"-"` // 内容类型，不序列化到JSON
	Content     []byte `json:"-"` // 文件内容，不序列化到JSON
}
