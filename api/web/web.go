// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package web

import (
	"context"

	"server/api/web/v1"
)

type IWebV1 interface {
	Tgrj(ctx context.Context, req *v1.TgrjReq) (res *v1.TgrjRes, err error)
	WangyiHot(ctx context.Context, req *v1.WangyiHotReq) (res *v1.<PERSON><PERSON><PERSON>otRes, err error)
	AnimePic(ctx context.Context, req *v1.AnimePicReq) (res *v1.AnimePicRes, err error)
	DouyinVideo(ctx context.Context, req *v1.DouyinVideoReq) (res *v1.DouyinVideoRes, err error)
	VipVideo(ctx context.Context, req *v1.VipVideoReq) (res *v1.VipVideoRes, err error)
}
