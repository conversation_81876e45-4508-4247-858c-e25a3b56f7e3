---
alwaysApply: true
---
## 🏗️ 项目结构规范

### 标准目录结构
```
/
├── api              # API接口定义
├── hack             # 开发工具配置
├── internal         # 内部实现
│   ├── cmd          # 命令行工具
│   ├── consts       # 常量定义
│   ├── controller   # 控制器
│   ├── dao          # 数据访问对象
│   ├── logic        # 业务逻辑
│   ├── model        # 数据模型
│   │   ├── do       # 数据传输对象
│   │   └── entity   # 实体对象
│   └── service      # 服务接口
├── manifest         # 配置文件
├── resource         # 资源文件
└── utility          # 工具包
```

## 🔄 开发流程规范

### 标准开发流程
1. **设计数据库表结构**（如需要）
2. **配置数据库连接** (hack/config.yaml)
3. **生成数据访问层**: `gf gen dao`
4. **定义API接口结构** (在 `api` 目录下)
5. **生成控制器骨架**: `gf gen ctrl`
6. **实现业务逻辑**
7. **注册路由** (internal/cmd/cmd.go)
8. **配置main.go导入**

### 架构模式选择

#### 简化架构 (小型项目)
- **架构层次**: Controller → Logic → DAO → Database
- **适用场景**: 简单CRUD操作、业务逻辑不复杂
- **无需service层**: 在controller中直接调用logic

#### 完整架构 (大型项目)
- **架构层次**: Controller → Service → Logic → DAO → Database
- **适用场景**: 复杂业务逻辑、需要服务复用
- **生成service接口**: `gf gen service`

## 📝 命名规范

### API接口命名
```go
// 请求结构体：XxxReq
type LoginReq struct {
    g.Meta `path:"/login" method:"post"`
    Username string `v:"required" json:"username"`
    Password string `v:"required" json:"password"`
}

// 响应结构体：XxxRes
type LoginRes struct {
    Token string `json:"token"`
}

// 常见命名模式：
// LoginReq/LoginRes         # 登录接口
// RegisterReq/RegisterRes   # 注册接口
// GetListReq/GetListRes     # 获取列表接口
// CreateReq/CreateRes       # 创建接口
// UpdateReq/UpdateRes       # 更新接口
// DeleteReq/DeleteRes       # 删除接口
```

### URI路由命名
```go
// 推荐使用默认方式 (小写+连字符)
// ShowList -> /user/show-list
s.SetNameToUriType(ghttp.UriTypeDefault)
```

### JSON标签命名
```bash
# 推荐使用CamelLower
gf gen dao -j CamelLower
```

## 🔗 必要配置

### main.go 必须导入
```go
package main

import (
    _ "github.com/gogf/gf/contrib/drivers/mysql/v2"  // MySQL驱动 (如果使用MySQL)
    _ "project-name/internal/logic"                   // 导入logic包 (必须)
    
    "github.com/gogf/gf/v2/os/gctx"
    "project-name/internal/cmd"
)

func main() {
    cmd.Main.Run(gctx.GetInitCtx())
}
```

### cmd.go 路由注册
```go
// internal/cmd/cmd.go
package cmd

import (
    "context"
    "github.com/gogf/gf/v2/frame/g"
    "github.com/gogf/gf/v2/net/ghttp"
    "github.com/gogf/gf/v2/os/gcmd"
    
    "project-name/internal/controller"
)

var (
    Main = gcmd.Command{
        Name:  "main",
        Usage: "main",
        Brief: "start http server",
        Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
            s := g.Server()
            s.Group("/", func(group *ghttp.RouterGroup) {
                group.Middleware(ghttp.MiddlewareHandlerResponse)
                group.Bind(
                    controller.Hello,  // 绑定控制器
                )
            })
            s.Run()
            return nil
        },
    }
)
```

## 🛠️ 代码生成工具使用

### gf gen dao
- **作用**: 生成数据访问层
- **使用时机**: 数据库表结构变更后
- **生成位置**: `internal/dao`, `internal/model`

### gf gen ctrl
- **作用**: 生成控制器骨架
- **使用时机**: API接口定义变更后
- **生成位置**: `internal/controller`

### gf gen service
- **作用**: 生成服务接口
- **使用时机**: 编写logic业务逻辑后
- **工作原理**: 解析`internal/logic`目录下的结构体和函数
- **生成位置**: `internal/service`

## 📋 开发检查清单

### 项目初始化
- [ ] 使用 `gf init project-name` 创建项目
- [ ] 配置数据库连接 (hack/config.yaml)
- [ ] 在main.go中添加必要的导入

### 开发过程
- [ ] 设计数据库表结构
- [ ] 运行 `gf gen dao` 生成数据访问层
- [ ] 在api目录定义接口结构
- [ ] 运行 `gf gen ctrl` 生成控制器
- [ ] 实现业务逻辑 (logic层)
- [ ] 如需要，运行 `gf gen service` 生成服务接口
- [ ] 注册路由 (internal/cmd/cmd.go)
- [ ] 测试接口功能

### 命名检查
- [ ] API接口使用 XxxReq/XxxRes 命名
- [ ] URI路由使用小写+连字符格式
- [ ] JSON标签使用CamelLower格式
- [ ] 目录结构符合官方标准

### 代码生成检查
- [ ] 确认没有手动修改工具生成的文件
- [ ] 检查生成文件的注释标记
- [ ] 自定义代码放在正确的位置
- [ ] 数据库变更后重新生成相关文件

## ⚠️ 重要注意事项


### 2.2 代码生成工具使用
1. 生成 dao 层代码：
```bash
gf gen dao
```

2. 生成控制器：
```bash
# 核心模块控制器
gf gen ctrl -m

# 插件模块控制器
gf gen ctrl -s=addons/xxx/api/api -d=addons/xxx/controller/api -m
```

3. 生成服务层：
```bash
# 核心模块服务
gf gen service -s=internal/logic -d=internal/service

# 插件模块服务
gf gen service -s=addons/xxx/logic -d=addons/xxx/service
```

4. 注意事项：
   - 生成前确保数据库连接配置正确
   - 修改数据库表结构后需要重新生成
   - 生成的代码不要手动修改，以免被覆盖

## 3. 核心开发指南 (internal/)

### 3.1 开发流程
1. 设计数据库表结构（如需要）
2. 使用 `gf gen dao` 生成数据访问层
3. 在 `api` 目录下定义接口
4. 使用 `gf gen ctrl` 生成控制器骨架
5. 在 `logic` 层实现业务逻辑
6. 使用 `gf gen service` 生成服务接口

### 3.2 控制器实现示例
```go
// internal/controller/user/user.go
func (c *cUser) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
    list, totalCount, err := service.User().GetList(ctx, &req.UserListInp)
    if err != nil {
        return nil, err
    }
    res = new(v1.ListRes)
    res.List = list
    res.PageRes.Pack(req, totalCount)
    return res, nil
}
```

### 3.3 逻辑层实现示例
```go
// internal/logic/user/user.go
type sUser struct{}

func init() {
    service.RegisterUser(NewUser())
}

func NewUser() *sUser {
    return &sUser{}
}

func (s *sUser) GetList(ctx context.Context, in *model.UserListInp) (res *model.UserListModel, totalCount int64, err error) {
    // 1. 业务逻辑处理
    
    // 2. 数据库查询
    m := dao.User.Ctx(ctx)
    if in.Username != "" {
        m = m.Where(dao.User.Columns.Username, in.Username)
    }
    
    // 3. 分页查询
    result, err := m.Page(in.Page, in.PageSize).ScanAndCount(&res,&totalCount,false)
    if err != nil {
        return nil, 0, gerror.Wrap(err, "获取用户列表失败")
    }
    
    // 4. 数据转换与返回
    return res, totalCount, nil
}
```

## 5. 数据模型与数据库操作

### 5.1 数据模型定义
```go
// 输入参数
type UserListInp struct {
    form.PageReq
    Username string `json:"username" dc:"用户名"`
    Status   int    `json:"status" dc:"状态"`
}

// 输出参数
type UserListModel struct {
    Username string `json:"username" dc:"用户名"`
    Status   int    `json:"status" dc:"状态"`
    ...
}
```

### 5.2 数据访问层结构
- `internal/dao/` - 数据访问对象（自动生成）
  - `internal/dao/internal/` - 自动生成的具体实现代码
- `internal/model/` - 数据模型定义
  - `do/` - 数据对象定义（自动生成）
  - `entity/` - 实体对象定义（自动生成）
  - `input/` - 输入参数对象（可手动修改）
    - `adminin/` - 管理后台参数
    - `apiin/` - 前端API参数

### 5.3 输入参数对象示例
```go
// 输入参数
type UserInp struct {
    Username string  `json:"username" dc:"用户名"`
}

// 输出参数
type UserModel struct {
    Username string `json:"username" dc:"用户名"`
    Mobile string `json:"mobile" dc:"手机号码"`
}
```

### 5.4 数据库操作规范
1. 所有字段查询必须使用 `dao.Table.Columns.FieldName`
2. 常用查询方法:
```go
// 单条记录查询
one, err := dao.User.Ctx(ctx).Where(dao.User.Columns.Id, id).One()

// 多条记录查询
list, err := dao.User.Ctx(ctx).Where(dao.User.Columns.Status, 1).All()

// 分页查询
list, totalCount, err := dao.User.Ctx(ctx).Page(page, pageSize).ScanAndCount(&result, &count, false)

// 条件构建查询
m := dao.User.Ctx(ctx)
if username != "" {
    m = m.Where(dao.User.Columns.Username, username)
}
if status > 0 {
    m = m.Where(dao.User.Columns.Status, status)
}
list, err := m.Order(dao.User.Columns.Id + " DESC").All()
```

3. 事务处理示例:
```go
err := dao.User.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
    // 事务操作1
    _, err := dao.User.Ctx(ctx).TX(tx).Data(data1).Insert()
    if err != nil {
        return err
    }
    
    // 事务操作2
    _, err = dao.UserLog.Ctx(ctx).TX(tx).Data(data2).Insert()
    if err != nil {
        return err
    }
    
    return nil
})
```

### 5.5 高级查询技巧
1. 关联查询:
```go
// 使用关联表查询
result, err := dao.User.Ctx(ctx).
    LeftJoin(dao.UserRole.Table+" r", "r.user_id="+dao.User.Table+"."+dao.User.Columns.Id).
    Fields(dao.User.Table+".*", "r.role_name").
    Where(dao.User.Columns.Status, 1).
    All()
```

2. 聚合查询:
```go
// 统计查询
count, err := dao.User.Ctx(ctx).Where(dao.User.Columns.Status, 1).Count()

// 聚合函数
result, err := dao.Order.Ctx(ctx).
    Fields("user_id, SUM(amount) as total_amount").
    Group("user_id").
    Having("SUM(amount) > ?", 1000).
    All()
```

## 6. API接口规范

### 6.1 API定义示例
```go
// api/v1/user.go
type ListReq struct {
    g.Meta `path:"/user/list" method:"get" tags:"用户管理" summary:"获取用户列表"`
    model.UserListInp
}

type ListRes struct {
    List  []*model.UserListModel `json:"list" dc:"数据列表"`
    *form.PageRes
}
```

### 6.2 API注解规范
- `path` - 接口路径
- `method` - 请求方法 (get, post, put, delete)
- `tags` - 接口分组
- `summary` - 接口描述
- `v` - 验证规则（如：required, min, max, length, range, email, phone）
- `dc` - 字段说明（用于生成API文档）

### 6.3 参数验证示例
```go
type CreateReq struct {
    g.Meta    `path:"/user/create" method:"post" tags:"用户管理" summary:"创建用户"`
    Username  string `v:"required|length:5,30#用户名不能为空|用户名长度应当在:min到:max之间" dc:"用户名"`
    Password  string `v:"required|length:6,30#密码不能为空|密码长度应当在:min到:max之间" dc:"密码"`
    Mobile    string `v:"required|phone#手机号不能为空|手机号格式不正确" dc:"手机号"`
    Email     string `v:"email#邮箱格式不正确" dc:"邮箱"`
    Status    int    `v:"in:0,1,2#状态值错误" dc:"状态"`
}
```

## 7. 错误处理规范

### 7.1 错误定义
```go
// internal/consts/error_code.go
const (
    CodeOK             = 0      // 成功
    CodeNotAuthorized  = 403    // 未授权
    CodeNotFound       = 404    // 资源不存在
    CodeServerError    = 500    // 服务器内部错误
    CodeBusinessError  = 10000  // 业务错误起始码
    
    // 用户模块错误码 (10100-10199)
    CodeUserNotFound   = 10100  // 用户不存在
    CodePasswordError  = 10101  // 密码错误
    // ...其他错误码
)
```

### 7.2 错误处理方式
1. 返回通用错误:
```go
return nil, gerror.New("用户名已存在")
```

2. 返回带错误码的错误:
```go
return nil, gerror.NewCode(consts.CodeUserNotFound, "用户不存在")
```

3. 包装错误并添加上下文:
```go
if err := doSomething(); err != nil {
    return nil, gerror.Wrap(err, "处理XXX失败")
}
```

4. 在控制器中处理错误:
```go
func (c *cUser) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
    data, err := service.User().Create(ctx, &req.CreateUserInp)
    if err != nil {
        return nil, err
    }
    return &v1.CreateRes{Id: data.Id}, nil
}
```


1. **导入logic包**: main.go中必须导入 `_ "project-name/internal/logic"`
2. **数据库驱动**: 根据使用的数据库导入相应驱动
3. **架构选择**: 根据项目复杂度选择合适的架构模式
4. **代码生成**: 数据库或API变更后及时重新生成相关代码
5. **路由注册**: 所有控制器必须在cmd.go中注册才能对外提供服务
6. **🚫 禁止修改工具生成的代码**: 避免手动修改gf工具生成的文件，否则会在下次生成时被覆盖

## 🚫 工具生成文件规则

### 禁止修改的生成文件
```bash
# gf gen dao 生成的文件 (每次运行都会覆盖)
internal/dao/internal/        # DAO内部实现文件
internal/model/do/           # 数据传输对象
internal/model/entity/       # 实体对象

# gf gen ctrl 生成的文件 (每次运行都会覆盖)
internal/controller/         # 控制器骨架文件 (如果已存在则不覆盖)

# gf gen service 生成的文件 (每次运行都会覆盖)
internal/service/            # 服务接口文件
```

### 文件识别方法
```go
// 生成的文件通常包含类似注释：
// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

// 或者包含包注释：
// Package do is auto-generated by GoFrame CLI tool.
```

### 正确的修改方式
```bash
# ❌ 错误：直接修改生成的文件
# 编辑 internal/dao/internal/user.go
# 编辑 internal/model/entity/user.go

# ✅ 正确：修改源文件后重新生成
# 1. 修改数据库表结构
# 2. 重新运行生成命令
gf gen dao

# ✅ 正确：在允许的位置添加自定义代码
# 编辑 internal/dao/user.go        # 可以添加自定义方法
# 编辑 internal/logic/user.go      # 实现业务逻辑
# 编辑 internal/controller/user.go # 添加控制器逻辑 (首次生成后)
```

### 最佳实践
1. **定期重新生成**: 数据库结构变更后立即重新生成
2. **版本控制**: 将生成的文件加入.gitignore或谨慎提交
3. **自定义扩展**: 在non-generated文件中添加自定义方法
4. **文档标记**: 在自定义文件中添加注释说明哪些是手动添加的



## 🔧 常用命令

```bash
# 项目初始化
gf init project-name

# 生成数据访问层
gf gen dao

# 生成控制器
gf gen ctrl

# 生成服务接口
gf gen service

# 运行项目 (开发模式)
gf run main.go

# 构建项目
gf build
```

遵循这些规范可以确保GoFrame项目的结构清晰、代码规范、易于维护。
